import requests
import time
import json
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dune_client.client import DuneClient

@dataclass
class LbPairInfo:
    """LbPair信息数据类"""
    address: str
    name: str
    mint_x: str
    mint_y: str
    hide: bool
    is_blacklisted: bool

@dataclass
class TokenRisk:
    """代币风险信息数据类"""
    token_id: str
    warnings: List[Dict[str, Any]]

class LbPairRiskAnalyzer:
    def __init__(self, dune_api_key: str, query_id: int):
        """
        初始化分析器

        Args:
            dune_api_key: Dune Analytics API密钥
            query_id: Dune查询ID
        """
        self.dune = DuneClient(dune_api_key)
        self.query_id = query_id
        self.meteora_base_url = "https://dlmm-api.meteora.ag/pair"
        self.jupiter_base_url = "https://ultra-api.jup.ag/shield"

        # 已知安全代币，无需检查风险
        self.safe_tokens = {
            "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",  # USDC
            "So11111111111111111111111111111111111111112"     # SOL
        }

        # API请求间隔（秒）
        self.request_delay = 0.5
        self.jupiter_delay = 1.0  # Jupiter API QPS限制为1

    def get_recent_lbpairs(self) -> List[str]:
        """
        从Dune获取最近几天创建的lbPair地址
        
        Returns:
            List[str]: lbPair地址列表
        """
        try:
            print("正在从Dune获取最近创建的lbPair数据...")
            result = self.dune.get_latest_result(self.query_id)
            
            if not result or not hasattr(result, 'result'):
                raise Exception("无法获取Dune查询结果")
            
            lbpairs = [row['lbPair'] for row in result.result.rows if 'lbPair' in row]
            print(f"获取到 {len(lbpairs)} 个lbPair地址")
            return lbpairs
            
        except Exception as e:
            print(f"获取Dune数据时发生错误: {e}")
            return []

    def get_lbpair_info(self, lbpair_address: str) -> Optional[LbPairInfo]:
        """
        从Meteora API获取lbPair详细信息
        
        Args:
            lbpair_address: lbPair地址
            
        Returns:
            LbPairInfo: lbPair信息对象，如果获取失败返回None
        """
        try:
            url = f"{self.meteora_base_url}/{lbpair_address}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                return LbPairInfo(
                    address=data.get('address', ''),
                    name=data.get('name', ''),
                    mint_x=data.get('mint_x', ''),
                    mint_y=data.get('mint_y', ''),
                    hide=data.get('hide', False),
                    is_blacklisted=data.get('is_blacklisted', False)
                )
            else:
                print(f"获取lbPair {lbpair_address} 信息失败，状态码: {response.status_code}")
                return None
                
        except Exception as e:
            print(f"获取lbPair {lbpair_address} 信息时发生错误: {e}")
            return None

    def get_token_risks(self, token_ids: List[str]) -> Dict[str, TokenRisk]:
        """
        从Jupiter API获取代币风险信息

        Args:
            token_ids: 代币ID列表

        Returns:
            Dict[str, TokenRisk]: 代币ID到风险信息的映射
        """
        if not token_ids:
            return {}

        # 过滤掉已知安全代币
        risky_tokens = [token_id for token_id in token_ids if token_id not in self.safe_tokens]

        # 为安全代币创建空风险记录
        token_risks = {}
        for safe_token in self.safe_tokens:
            if safe_token in token_ids:
                token_risks[safe_token] = TokenRisk(token_id=safe_token, warnings=[])

        if not risky_tokens:
            return token_risks

        try:
            # 将token_ids转为逗号分隔的字符串
            mints_param = ",".join(risky_tokens)
            url = f"{self.jupiter_base_url}?mints={mints_param}"

            print(f"正在查询 {len(risky_tokens)} 个代币的风险信息...")
            response = requests.get(url, timeout=15)

            # Jupiter API QPS限制
            time.sleep(self.jupiter_delay)

            if response.status_code == 200:
                data = response.json()
                warnings_data = data.get('warnings', {})

                for token_id in risky_tokens:
                    warnings = warnings_data.get(token_id, [])
                    # 只保留critical和warning级别的警告
                    filtered_warnings = [
                        w for w in warnings
                        if w.get('severity', '').lower() in ['critical', 'warning']
                    ]
                    token_risks[token_id] = TokenRisk(
                        token_id=token_id,
                        warnings=filtered_warnings
                    )

                return token_risks
            else:
                print(f"获取代币风险信息失败，状态码: {response.status_code}")
                # 为失败的代币创建空风险记录
                for token_id in risky_tokens:
                    token_risks[token_id] = TokenRisk(token_id=token_id, warnings=[])
                return token_risks

        except Exception as e:
            print(f"获取代币风险信息时发生错误: {e}")
            # 为失败的代币创建空风险记录
            for token_id in risky_tokens:
                token_risks[token_id] = TokenRisk(token_id=token_id, warnings=[])
            return token_risks

    def analyze_risk_severity(self, warnings: List[Dict[str, Any]]) -> str:
        """
        分析风险严重程度

        Args:
            warnings: 警告列表

        Returns:
            str: 风险等级 (safe, low, medium, high, critical)
        """
        if not warnings:
            return "safe"

        severity_levels = [warning.get('severity', 'info').lower() for warning in warnings]

        if 'critical' in severity_levels:
            return "critical"
        elif 'warning' in severity_levels:
            return "warning"
        else:
            return "safe"

    def format_risk_report(self, lbpair_info: LbPairInfo, x_risk: TokenRisk, y_risk: TokenRisk) -> Dict[str, Any]:
        """
        格式化风险报告
        
        Args:
            lbpair_info: lbPair信息
            x_risk: mint_x代币风险
            y_risk: mint_y代币风险
            
        Returns:
            Dict[str, Any]: 格式化的风险报告
        """
        return {
            "lbpair": {
                "address": lbpair_info.address,
                "name": lbpair_info.name,
                "hide": lbpair_info.hide,
                "is_blacklisted": lbpair_info.is_blacklisted
            },
            "tokens": {
                "mint_x": {
                    "address": lbpair_info.mint_x,
                    "risk_level": self.analyze_risk_severity(x_risk.warnings),
                    "warnings_count": len(x_risk.warnings),
                    "warnings": x_risk.warnings
                },
                "mint_y": {
                    "address": lbpair_info.mint_y,
                    "risk_level": self.analyze_risk_severity(y_risk.warnings),
                    "warnings_count": len(y_risk.warnings),
                    "warnings": y_risk.warnings
                }
            },
            "overall_risk": max(
                self.analyze_risk_severity(x_risk.warnings),
                self.analyze_risk_severity(y_risk.warnings),
                key=lambda x: ["safe", "warning", "critical"].index(x)
            )
        }

    def run_analysis(self) -> List[Dict[str, Any]]:
        """
        执行完整的分析流程
        
        Returns:
            List[Dict[str, Any]]: 分析结果列表
        """
        results = []
        
        # 1. 获取lbPair列表
        lbpair_addresses = self.get_recent_lbpairs()
        
        if not lbpair_addresses:
            print("未获取到lbPair数据")
            return results
        
        # 2. 获取每个lbPair的详细信息
        lbpair_infos = []
        all_token_ids = set()
        
        print(f"\n正在获取 {len(lbpair_addresses)} 个lbPair的详细信息...")
        for i, address in enumerate(lbpair_addresses, 1):
            print(f"处理第 {i}/{len(lbpair_addresses)} 个: {address}")
            
            lbpair_info = self.get_lbpair_info(address)
            if lbpair_info:
                lbpair_infos.append(lbpair_info)
                all_token_ids.add(lbpair_info.mint_x)
                all_token_ids.add(lbpair_info.mint_y)
            
            # 避免API限制
            time.sleep(self.request_delay)
        
        print(f"成功获取到 {len(lbpair_infos)} 个lbPair信息")
        
        # 3. 批量获取代币风险信息
        print(f"\n正在获取 {len(all_token_ids)} 个代币的风险信息...")
        token_risks = self.get_token_risks(list(all_token_ids))
        
        # 4. 生成分析报告
        print("\n正在生成分析报告...")
        for lbpair_info in lbpair_infos:
            x_risk = token_risks.get(lbpair_info.mint_x, TokenRisk(lbpair_info.mint_x, []))
            y_risk = token_risks.get(lbpair_info.mint_y, TokenRisk(lbpair_info.mint_y, []))
            
            report = self.format_risk_report(lbpair_info, x_risk, y_risk)
            results.append(report)
        
        return results

    def save_results(self, results: List[Dict[str, Any]], filename: str = "lbpair_risk_analysis.json"):
        """
        保存分析结果到文件
        
        Args:
            results: 分析结果
            filename: 保存文件名
        """
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            print(f"结果已保存到 {filename}")
        except Exception as e:
            print(f"保存文件时发生错误: {e}")

    def print_summary(self, results: List[Dict[str, Any]]):
        """
        打印分析摘要
        
        Args:
            results: 分析结果
        """
        if not results:
            print("没有分析结果")
            return
        
        print(f"\n=== 分析摘要 ===")
        print(f"总计分析lbPair数量: {len(results)}")
        
        # 风险等级统计
        risk_counts = {"safe": 0, "low": 0, "medium": 0, "high": 0, "critical": 0}
        blacklisted_count = 0
        
        for result in results:
            risk_level = result["overall_risk"]
            risk_counts[risk_level] += 1
            
            if result["lbpair"]["is_blacklisted"]:
                blacklisted_count += 1
        
        print(f"风险等级分布:")
        for level, count in risk_counts.items():
            if count > 0:
                print(f"  {level.upper()}: {count}")
        
        print(f"黑名单lbPair数量: {blacklisted_count}")
        
        # 高风险lbPair
        high_risk_pairs = [r for r in results if r["overall_risk"] in ["high", "critical"]]
        if high_risk_pairs:
            print(f"\n高风险lbPair ({len(high_risk_pairs)}个):")
            for pair in high_risk_pairs:
                print(f"  {pair['lbpair']['name']} ({pair['lbpair']['address']}) - {pair['overall_risk'].upper()}")


# 使用示例
if __name__ == "__main__":
    # 配置参数
    DUNE_API_KEY = "9zHvfPzBSK6S3VIt0MS9XznTROqRoKGs"  # 替换为你的Dune API密钥
    QUERY_ID = "5658230"  # 替换为你的Dune查询ID
    
    # 创建分析器
    analyzer = LbPairRiskAnalyzer(DUNE_API_KEY, QUERY_ID)
    
    # 执行分析
    results = analyzer.run_analysis()
    
    # 打印摘要
    analyzer.print_summary(results)
    
    # 保存结果
    analyzer.save_results(results)
    
    # 打印部分详细结果
    if results:
        print(f"\n=== 详细结果示例 ===")
        print(json.dumps(results[0], indent=2, ensure_ascii=False))